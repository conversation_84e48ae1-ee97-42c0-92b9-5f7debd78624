# Account Location Comparison API

## Overview

The Account Location Comparison API provides comprehensive location analysis for customer accounts by comparing registered account locations with actual visit locations. This helps identify patterns and discrepancies in location data.

## Features

- **Location Analysis**: Compare registered account coordinates with actual visit coordinates
- **Distance Calculations**: Calculate precise distances between registered and visit locations
- **Pattern Detection**: Identify accounts with significant location deviations
- **Flexible Filtering**: Support for account-specific analysis and date range filtering
- **Summary Statistics**: Comprehensive reporting with variance analysis
- **Laravel Octane Compatible**: Stateless design with proper dependency injection

## API Endpoints

### 1. Generate Location Comparison Report

**Endpoint:** `POST /api/reports/account-location-comparison`

**Description:** Generate a comprehensive location comparison report for customer accounts within LineDivision hierarchy.

#### Request Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `line_ids` | array | No | null | Array of Line IDs to filter accounts by LineDivision hierarchy |
| `div_ids` | array | No | null | Array of Division IDs to filter accounts by specific LineDivisions |
| `account_ids` | array | No | null | Specific account IDs to analyze (can be combined with line/division filtering) |
| `from_date` | string | No | null | Start date for visit analysis (YYYY-MM-DD format) |
| `to_date` | string | No | null | End date for visit analysis (YYYY-MM-DD format) |
| `distance_threshold` | number | No | 1000 | Distance threshold in meters for flagging deviations |
| `recent_visits_limit` | integer | No | 5 | Number of recent visits to analyze per account (1-50) |

#### Example Requests

**Filter by Multiple Lines:**
```json
{
    "line_ids": [1, 2, 3],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 1500,
    "recent_visits_limit": 10
}
```

**Filter by Multiple Divisions:**
```json
{
    "line_ids": [1, 2],
    "div_ids": [10, 20, 30],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 1500,
    "recent_visits_limit": 10
}
```

**Filter by Specific Accounts:**
```json
{
    "account_ids": [123, 456, 789],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 1500,
    "recent_visits_limit": 10
}
```

**Combined Filtering:**
```json
{
    "line_ids": [1, 2],
    "div_ids": [10, 20],
    "account_ids": [123, 456],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 2000,
    "recent_visits_limit": 15
}
```

#### Response Format

```json
{
    "success": true,
    "message": "Location comparison report generated successfully",
    "data": {
        "success": true,
        "summary": {
            "total_accounts_analyzed": 1,
            "accounts_with_discrepancies": 0,
            "total_visits_analyzed": 5,
            "average_distance_variance": 250.5,
            "max_distance_variance": 800.0,
            "accounts_without_registered_location": 0,
            "accounts_without_visits": 0
        },
        "accounts": [
            {
                "account_id": 123,
                "account_name": "Test Pharmacy",
                "account_code": "PH001",
                "account_address": "123 Main Street",
                "registered_location": {
                    "has_location": true,
                    "latitude": 31.2001,
                    "longitude": 29.9187,
                    "source": "account_lines",
                    "from_date": "2024-01-01T00:00:00.000000Z",
                    "to_date": null
                },
                "visit_locations": [
                    {
                        "visit_id": 456,
                        "visit_date": "2024-01-15",
                        "latitude": 31.2050,
                        "longitude": 29.9200,
                        "visit_address": "Near Test Pharmacy",
                        "distance_from_registered": 500.25,
                        "exceeds_threshold": false,
                        "visit_frequency": 3,
                        "user": {
                            "id": 123,
                            "name": "John Doe",
                            "email": "<EMAIL>"
                        }
                    }
                ],
                "visits_count": 5,
                "has_discrepancies": false,
                "max_distance_from_registered": 800.0,
                "distance_threshold_used": 1500
            }
        ],
        "generated_at": "2024-01-31T12:00:00.000000Z",
        "parameters": {
            "account_id": 123,
            "from_date": "2024-01-01",
            "to_date": "2024-01-31",
            "distance_threshold": 1500,
            "recent_visits_limit": 10
        }
    }
}
```

### 2. Get Report Information

**Endpoint:** `GET /api/reports/account-location-comparison/info`

**Description:** Get detailed information about the report parameters and response format.

#### Response Format

```json
{
    "success": true,
    "message": "Report information retrieved successfully",
    "data": {
        "description": "Generate location comparison reports for customer accounts",
        "parameters": {
            "account_id": {
                "type": "integer",
                "required": false,
                "description": "Specific account ID to analyze"
            },
            "from_date": {
                "type": "string",
                "format": "YYYY-MM-DD",
                "required": false,
                "description": "Start date for visit data analysis"
            }
        },
        "response_format": {
            "summary": "Summary statistics object",
            "accounts": "Array of account analysis objects"
        }
    }
}
```

## Data Sources

### Account Locations
- **Primary Source**: `account_lines` table with active location records
- **Fallback Source**: `accounts` table location fields
- **Coordinates**: Uses `ll` (latitude) and `lg` (longitude) fields

### Visit Locations
- **Source**: `actual_visits` table
- **Filtering**: Only visits with valid coordinates (non-null, non-zero)
- **Deduplication**: Filters for distinct coordinates to avoid duplicate locations

## Distance Calculation

The API uses the Haversine formula to calculate distances between coordinates:
- **Unit**: Meters
- **Precision**: Rounded to 2 decimal places
- **Service**: `DifferenceBetweenTwoCoordinates` service

## Visit Frequency Analysis

Each visit location includes a `visit_frequency` field that provides insights into location usage patterns:

### Frequency Calculation
- **Radius**: 100 meters from the visit coordinate
- **Scope**: All visits for the account (not limited by date filters)
- **Count**: Total number of visits within the 100-meter radius
- **Includes**: The current visit plus any other visits within the radius

### Use Cases
- **High-Traffic Locations**: Identify frequently visited coordinates (frequency > 5)
- **Occasional Visits**: Detect one-time or rare visit locations (frequency = 1-2)
- **Location Clustering**: Understand visit patterns and customer behavior
- **Route Optimization**: Identify optimal visit locations based on frequency

### Performance Considerations
- Frequency calculation is optimized to avoid N+1 query problems
- Performance logging is enabled for accounts with >50 visits
- Calculation time is monitored and logged for optimization

## User Information

Each visit location includes user details about who performed the visit:

### User Data Fields
- **id**: User ID from the users table
- **name**: Full name of the user who performed the visit
- **email**: Email address of the user (if available)

### Data Source
- **Primary**: `users` table joined with `actual_visits.user_id`
- **Fallback**: "Unknown User" for missing or deleted user records
- **Query Optimization**: Uses LEFT JOIN to avoid excluding visits with missing users

### Error Handling
- **Missing Users**: Displays "Unknown User" when user data is unavailable
- **Deleted Users**: Gracefully handles soft-deleted or removed user accounts
- **Data Integrity**: Service continues working even if user lookup fails

### Use Cases
- **Performance Tracking**: Identify which users are visiting specific locations
- **Route Analysis**: Analyze user-specific visit patterns and efficiency
- **Accountability**: Track visit responsibility and user performance
- **Team Management**: Understand team member coverage and activity

## Error Handling

### Validation Errors (422)
```json
{
    "success": false,
    "message": "Invalid parameters provided",
    "errors": {
        "account_id": "Account ID must be a positive integer",
        "from_date": "Invalid from_date format. Use YYYY-MM-DD"
    }
}
```

### Service Errors (500)
```json
{
    "success": false,
    "message": "Failed to generate report",
    "errors": {
        "message": "Database connection failed"
    }
}
```

## Usage Examples

### Analyze All Accounts
```bash
curl -X POST /api/reports/account-location-comparison \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{}'
```

### Analyze Accounts by Multiple Lines
```bash
curl -X POST /api/reports/account-location-comparison \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "line_ids": [1, 2, 3],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 2000
  }'
```

### Analyze Accounts by Multiple Divisions
```bash
curl -X POST /api/reports/account-location-comparison \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "line_ids": [1, 2],
    "div_ids": [10, 20, 30],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 2000
  }'
```

### Analyze Specific Accounts with Date Range
```bash
curl -X POST /api/reports/account-location-comparison \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "account_ids": [123, 456, 789],
    "from_date": "2024-01-01",
    "to_date": "2024-01-31",
    "distance_threshold": 2000
  }'
```

### Get Report Information
```bash
curl -X GET /api/reports/account-location-comparison/info \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Testing

The API includes comprehensive unit tests:
- **Service Tests**: `tests/Unit/Services/AccountLocationComparisonServiceTest.php`
- **Controller Tests**: `tests/Unit/Http/Controllers/AccountLocationComparisonControllerTest.php`

Run tests using Laravel Sail:
```bash
./vendor/bin/sail artisan test tests/Unit/Services/AccountLocationComparisonServiceTest.php
./vendor/bin/sail artisan test tests/Unit/Http/Controllers/AccountLocationComparisonControllerTest.php
```

## Laravel Octane Compatibility

The implementation follows Laravel Octane best practices:
- **Stateless Design**: No static state or class properties
- **Dependency Injection**: Proper service container bindings
- **Memory Management**: No persistent memory state between requests
- **Service Bindings**: Uses `bind()` instead of `singleton()` for stateful services
