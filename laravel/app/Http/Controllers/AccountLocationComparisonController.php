<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ApiController;
use App\Services\AccountLocationComparisonService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * Controller for handling account location comparison report API requests
 * 
 * This controller provides endpoints for generating location comparison reports
 * that analyze the geographical differences between registered account locations
 * and actual visit locations.
 * 
 * Features:
 * - Generate comprehensive location comparison reports
 * - Support filtering by account ID, date ranges, and distance thresholds
 * - Provide detailed analysis with summary statistics
 * - Handle error cases with proper HTTP status codes
 * 
 * Laravel Octane Compatible:
 * - Stateless design
 * - Proper dependency injection
 * - No static state
 */
class AccountLocationComparisonController extends ApiController
{
    /**
     * Account location comparison service
     */
    private AccountLocationComparisonService $locationComparisonService;

    /**
     * Constructor with dependency injection
     *
     * @param AccountLocationComparisonService $locationComparisonService
     */
    public function __construct(AccountLocationComparisonService $locationComparisonService)
    {
        $this->locationComparisonService = $locationComparisonService;
    }

    /**
     * Generate location comparison report
     *
     * @param Request $request HTTP request with report parameters
     * @return JsonResponse JSON response with report data
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            Log::info('Account location comparison report requested', [
                'user_id' => auth()->id(),
                'parameters' => $request->all()
            ]);

            // Validate request parameters
            $validatedData = $this->validateRequest($request);

            // Validate business logic parameters
            $validation = $this->locationComparisonService->validateReportParameters($validatedData);
            
            if (!$validation['valid']) {
                Log::warning('Invalid parameters for location comparison report', [
                    'errors' => $validation['errors'],
                    'user_id' => auth()->id()
                ]);

                return $this->respondWithError(
                    'Invalid parameters provided',
                    $validation['errors'],
                    422
                );
            }

            // Generate the report
            $report = $this->locationComparisonService->generateLocationComparisonReport($validatedData);

            if (!$report['success']) {
                Log::error('Failed to generate location comparison report', [
                    'error' => $report['error'] ?? 'Unknown error',
                    'user_id' => auth()->id()
                ]);

                return $this->respondWithError(
                    'Failed to generate report',
                    ['message' => $report['error'] ?? 'Unknown error occurred'],
                    500
                );
            }

            Log::info('Location comparison report generated successfully', [
                'accounts_analyzed' => $report['summary']['total_accounts_analyzed'],
                'accounts_with_discrepancies' => $report['summary']['accounts_with_discrepancies'],
                'user_id' => auth()->id()
            ]);

            return $this->respondWithSuccess(
                'Location comparison report generated successfully',
                $report
            );

        } catch (ValidationException $e) {
            Log::warning('Validation failed for location comparison report', [
                'errors' => $e->errors(),
                'user_id' => auth()->id()
            ]);

            return $this->respondWithError(
                'Validation failed',
                $e->errors(),
                422
            );

        } catch (\Exception $e) {
            Log::error('Unexpected error generating location comparison report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);

            return $this->respondWithError(
                'An unexpected error occurred',
                ['message' => 'Please try again later or contact support'],
                500
            );
        }
    }

    /**
     * Get report parameters information
     *
     * @return JsonResponse JSON response with parameter information
     */
    public function getReportInfo(): JsonResponse
    {
        try {
            $info = [
                'description' => 'Generate location comparison reports for customer accounts within LineDivision hierarchy',
                'parameters' => [
                    'line_ids' => [
                        'type' => 'array',
                        'required' => false,
                        'description' => 'Array of Line IDs to filter accounts by LineDivision hierarchy (optional)',
                        'example' => [1, 2, 3]
                    ],
                    'div_ids' => [
                        'type' => 'array',
                        'required' => false,
                        'description' => 'Array of Division IDs to filter accounts by specific LineDivisions (optional)',
                        'example' => [10, 20, 30]
                    ],
                    'account_ids' => [
                        'type' => 'array',
                        'required' => false,
                        'description' => 'Specific account IDs to analyze (optional, can be combined with line/division filtering)',
                        'example' => [123, 456, 789]
                    ],
                    'from_date' => [
                        'type' => 'string',
                        'format' => 'YYYY-MM-DD',
                        'required' => false,
                        'description' => 'Start date for visit data analysis (optional)'
                    ],
                    'to_date' => [
                        'type' => 'string',
                        'format' => 'YYYY-MM-DD',
                        'required' => false,
                        'description' => 'End date for visit data analysis (optional)'
                    ],
                    'distance_threshold' => [
                        'type' => 'number',
                        'required' => false,
                        'default' => 1000,
                        'description' => 'Distance threshold in meters for flagging significant deviations'
                    ],
                    'recent_visits_limit' => [
                        'type' => 'integer',
                        'required' => false,
                        'default' => 5,
                        'min' => 1,
                        'max' => 50,
                        'description' => 'Number of recent visits to analyze per account'
                    ]
                ],
                'response_format' => [
                    'success' => 'boolean',
                    'summary' => [
                        'total_accounts_analyzed' => 'integer',
                        'accounts_with_discrepancies' => 'integer',
                        'total_visits_analyzed' => 'integer',
                        'average_distance_variance' => 'number',
                        'max_distance_variance' => 'number',
                        'accounts_without_registered_location' => 'integer',
                        'accounts_without_visits' => 'integer'
                    ],
                    'accounts' => [
                        'account_id' => 'integer',
                        'account_name' => 'string',
                        'account_code' => 'string',
                        'registered_location' => 'object',
                        'visit_locations' => 'array',
                        'has_discrepancies' => 'boolean',
                        'max_distance_from_registered' => 'number'
                    ]
                ]
            ];

            return $this->respondWithSuccess(
                'Report information retrieved successfully',
                $info
            );

        } catch (\Exception $e) {
            Log::error('Error retrieving report information', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->respondWithError(
                'Failed to retrieve report information',
                ['message' => 'Please try again later'],
                500
            );
        }
    }

    /**
     * Validate request parameters
     *
     * @param Request $request HTTP request to validate
     * @return array Validated parameters
     * @throws ValidationException If validation fails
     */
    private function validateRequest(Request $request): array
    {
        return $request->validate([
            'line_ids' => 'nullable|array',
            'line_ids.*' => 'nullable|integer|min:1',
            'div_ids' => 'nullable|array',
            'div_ids.*' => 'nullable|integer|min:1',
            'account_ids' => 'nullable|array',
            'account_ids.*' => 'nullable|integer|min:1',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d|after_or_equal:from_date',
            'distance_threshold' => 'nullable|numeric|min:0',
            'recent_visits_limit' => 'nullable|integer|min:1|max:50'
        ], [
            'line_ids.array' => 'Line IDs must be provided as an array',
            'line_ids.*.integer' => 'Each Line ID must be a valid integer',
            'line_ids.*.min' => 'Each Line ID must be greater than 0',
            'div_ids.array' => 'Division IDs must be provided as an array',
            'div_ids.*.integer' => 'Each Division ID must be a valid integer',
            'div_ids.*.min' => 'Each Division ID must be greater than 0',
            'account_ids.*.integer' => 'Account ID must be a valid integer',
            'account_ids.*.min' => 'Account ID must be greater than 0',
            'from_date.date_format' => 'From date must be in YYYY-MM-DD format',
            'to_date.date_format' => 'To date must be in YYYY-MM-DD format',
            'to_date.after_or_equal' => 'To date must be equal to or after from date',
            'distance_threshold.numeric' => 'Distance threshold must be a valid number',
            'distance_threshold.min' => 'Distance threshold cannot be negative',
            'recent_visits_limit.integer' => 'Recent visits limit must be a valid integer',
            'recent_visits_limit.min' => 'Recent visits limit must be at least 1',
            'recent_visits_limit.max' => 'Recent visits limit cannot exceed 50'
        ]);
    }

    /**
     * Respond with success
     *
     * @param string $message Success message
     * @param array $data Response data
     * @return JsonResponse JSON response
     */
    private function respondWithSuccess(string $message, array $data): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], 200);
    }

    /**
     * Respond with error
     *
     * @param string $message Error message
     * @param array $errors Error details
     * @param int $statusCode HTTP status code
     * @return JsonResponse JSON response
     */
    private function respondWithError(string $message, array $errors, int $statusCode): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
}
