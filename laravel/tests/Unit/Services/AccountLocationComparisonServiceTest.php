<?php

namespace Tests\Unit\Services;

use App\Account;
use App\AccountLines;
use App\ActualVisit;
use App\Line;
use App\LineDivision;
use App\Services\AccountLocationComparisonService;
use App\Services\DifferenceBetweenTwoCoordinates;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Test class for AccountLocationComparisonService
 *
 * Tests the location comparison service functionality including report generation,
 * distance calculations, and data analysis with proper dependency mocking
 * for Laravel Octane compatibility.
 *
 * @covers \App\Services\AccountLocationComparisonService
 */
class AccountLocationComparisonServiceTest extends TestCase
{
    private AccountLocationComparisonService $service;
    private DifferenceBetweenTwoCoordinates $mockDistanceCalculator;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockDistanceCalculator = Mockery::mock(DifferenceBetweenTwoCoordinates::class);
        $this->service = new AccountLocationComparisonService($this->mockDistanceCalculator);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful report generation with valid data
     */
    public function test_generate_location_comparison_report_success(): void
    {
        // Mock Account
        $account = Mockery::mock(Account::class);
        $account->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $account->shouldReceive('getAttribute')->with('name')->andReturn('Test Account');
        $account->shouldReceive('getAttribute')->with('code')->andReturn('ACC001');
        $account->shouldReceive('getAttribute')->with('address')->andReturn('Test Address');
        $account->shouldReceive('getAttribute')->with('ll')->andReturn('31.2001');
        $account->shouldReceive('getAttribute')->with('lg')->andReturn('29.9187');

        // Mock AccountLines
        $accountLine = Mockery::mock(AccountLines::class);
        $accountLine->shouldReceive('getAttribute')->with('ll')->andReturn('31.2001');
        $accountLine->shouldReceive('getAttribute')->with('lg')->andReturn('29.9187');
        $accountLine->shouldReceive('getAttribute')->with('from_date')->andReturn(Carbon::now()->subDays(30));
        $accountLine->shouldReceive('getAttribute')->with('to_date')->andReturn(null);

        // Mock ActualVisit with user information
        $visit = Mockery::mock(ActualVisit::class);
        $visit->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $visit->shouldReceive('getAttribute')->with('visit_date')->andReturn(Carbon::now()->subDays(1));
        $visit->shouldReceive('getAttribute')->with('ll')->andReturn('31.2050');
        $visit->shouldReceive('getAttribute')->with('lg')->andReturn('29.9200');
        $visit->shouldReceive('getAttribute')->with('visit_address')->andReturn('Visit Address');
        $visit->shouldReceive('getAttribute')->with('user_id')->andReturn(123);
        $visit->shouldReceive('getAttribute')->with('user_name')->andReturn('John Doe');
        $visit->shouldReceive('getAttribute')->with('user_email')->andReturn('<EMAIL>');

        // Mock Eloquent queries
        Account::shouldReceive('query')->andReturnSelf();
        Account::shouldReceive('select')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('get')->andReturn(collect([$account]));

        AccountLines::shouldReceive('where')->andReturnSelf();
        AccountLines::shouldReceive('whereNotNull')->andReturnSelf();
        AccountLines::shouldReceive('orderBy')->andReturnSelf();
        AccountLines::shouldReceive('first')->andReturn($accountLine);

        ActualVisit::shouldReceive('where')->andReturnSelf();
        ActualVisit::shouldReceive('whereNotNull')->andReturnSelf();
        ActualVisit::shouldReceive('whereNull')->andReturnSelf();
        ActualVisit::shouldReceive('select')->andReturnSelf();
        ActualVisit::shouldReceive('orderBy')->andReturnSelf();
        ActualVisit::shouldReceive('limit')->andReturnSelf();
        ActualVisit::shouldReceive('get')->andReturn(collect([$visit]));

        // Mock distance calculations
        $this->mockDistanceCalculator
            ->shouldReceive('distanceBetweenTwoCoordinates')
            ->andReturn(500.0, 0.0); // First call for registered location, second for frequency

        // Mock Log facade
        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('debug')->andReturnNull();

        $options = [
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $result = $this->service->generateLocationComparisonReport($options);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('accounts', $result);
        $this->assertEquals(1, $result['summary']['total_accounts_analyzed']);
        $this->assertEquals(0, $result['summary']['accounts_with_discrepancies']);
        $this->assertEquals(1, count($result['accounts']));

        // Verify user information is included
        $visitLocation = $result['accounts'][0]['visit_locations'][0];
        $this->assertArrayHasKey('user', $visitLocation);
        $this->assertEquals(123, $visitLocation['user']['id']);
        $this->assertEquals('John Doe', $visitLocation['user']['name']);
        $this->assertEquals('<EMAIL>', $visitLocation['user']['email']);
    }

    /**
     * Test report generation with no accounts found
     */
    public function test_generate_location_comparison_report_no_accounts(): void
    {
        // Mock empty account collection
        Account::shouldReceive('query')->andReturnSelf();
        Account::shouldReceive('select')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('get')->andReturn(collect([]));

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('warning')->andReturnNull();

        $result = $this->service->generateLocationComparisonReport();

        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['summary']['total_accounts_analyzed']);
        $this->assertEmpty($result['accounts']);
    }

    /**
     * Test report generation with distance threshold exceeded
     */
    public function test_generate_location_comparison_report_with_discrepancies(): void
    {
        // Mock Account
        $account = Mockery::mock(Account::class);
        $account->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $account->shouldReceive('getAttribute')->with('name')->andReturn('Test Account');
        $account->shouldReceive('getAttribute')->with('code')->andReturn('ACC001');
        $account->shouldReceive('getAttribute')->with('address')->andReturn('Test Address');
        $account->shouldReceive('getAttribute')->with('ll')->andReturn('31.2001');
        $account->shouldReceive('getAttribute')->with('lg')->andReturn('29.9187');

        // Mock AccountLines
        $accountLine = Mockery::mock(AccountLines::class);
        $accountLine->shouldReceive('getAttribute')->with('ll')->andReturn('31.2001');
        $accountLine->shouldReceive('getAttribute')->with('lg')->andReturn('29.9187');
        $accountLine->shouldReceive('getAttribute')->with('from_date')->andReturn(Carbon::now()->subDays(30));
        $accountLine->shouldReceive('getAttribute')->with('to_date')->andReturn(null);

        // Mock ActualVisit with far coordinates
        $visit = Mockery::mock(ActualVisit::class);
        $visit->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $visit->shouldReceive('getAttribute')->with('visit_date')->andReturn(Carbon::now()->subDays(1));
        $visit->shouldReceive('getAttribute')->with('ll')->andReturn('31.3000');
        $visit->shouldReceive('getAttribute')->with('lg')->andReturn('30.0000');
        $visit->shouldReceive('getAttribute')->with('visit_address')->andReturn('Far Visit Address');

        // Mock Eloquent queries
        Account::shouldReceive('query')->andReturnSelf();
        Account::shouldReceive('select')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('get')->andReturn(collect([$account]));

        AccountLines::shouldReceive('where')->andReturnSelf();
        AccountLines::shouldReceive('whereNotNull')->andReturnSelf();
        AccountLines::shouldReceive('orderBy')->andReturnSelf();
        AccountLines::shouldReceive('first')->andReturn($accountLine);

        ActualVisit::shouldReceive('where')->andReturnSelf();
        ActualVisit::shouldReceive('whereNotNull')->andReturnSelf();
        ActualVisit::shouldReceive('whereNull')->andReturnSelf();
        ActualVisit::shouldReceive('select')->andReturnSelf();
        ActualVisit::shouldReceive('orderBy')->andReturnSelf();
        ActualVisit::shouldReceive('limit')->andReturnSelf();
        ActualVisit::shouldReceive('get')->andReturn(collect([$visit]));

        // Mock distance calculation - exceeds threshold
        $this->mockDistanceCalculator
            ->shouldReceive('distanceBetweenTwoCoordinates')
            ->andReturn(1500.0, 0.0); // First call for registered location, second for frequency

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('debug')->andReturnNull();

        $options = [
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $result = $this->service->generateLocationComparisonReport($options);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['summary']['accounts_with_discrepancies']);
        $this->assertTrue($result['accounts'][0]['has_discrepancies']);
        $this->assertTrue($result['accounts'][0]['visit_locations'][0]['exceeds_threshold']);
    }

    /**
     * Test parameter validation with valid parameters
     */
    public function test_validate_report_parameters_valid(): void
    {
        Account::shouldReceive('where')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('exists')->andReturn(true);

        $options = [
            'account_id' => 1,
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $result = $this->service->validateReportParameters($options);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test parameter validation with invalid parameters
     */
    public function test_validate_report_parameters_invalid(): void
    {
        $options = [
            'account_id' => -1,
            'from_date' => 'invalid-date',
            'to_date' => '2023-01-01',
            'distance_threshold' => -100,
            'recent_visits_limit' => 100
        ];

        $result = $this->service->validateReportParameters($options);

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('account_id', $result['errors']);
        $this->assertArrayHasKey('from_date', $result['errors']);
        $this->assertArrayHasKey('distance_threshold', $result['errors']);
        $this->assertArrayHasKey('recent_visits_limit', $result['errors']);
    }

    /**
     * Test error handling during report generation
     */
    public function test_generate_location_comparison_report_error_handling(): void
    {
        // Mock exception during account query
        Account::shouldReceive('query')->andThrow(new \Exception('Database error'));

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('error')->andReturnNull();

        $result = $this->service->generateLocationComparisonReport();

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
        $this->assertStringContainsString('Database error', $result['error']);
    }

    /**
     * Test visit frequency calculation
     */
    public function test_visit_frequency_calculation(): void
    {
        // Mock Account
        $account = Mockery::mock(Account::class);
        $account->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $account->shouldReceive('getAttribute')->with('name')->andReturn('Test Account');
        $account->shouldReceive('getAttribute')->with('code')->andReturn('ACC001');
        $account->shouldReceive('getAttribute')->with('address')->andReturn('Test Address');

        // Mock AccountLines (no registered location for simplicity)
        AccountLines::shouldReceive('where')->andReturnSelf();
        AccountLines::shouldReceive('whereNotNull')->andReturnSelf();
        AccountLines::shouldReceive('orderBy')->andReturnSelf();
        AccountLines::shouldReceive('first')->andReturn(null);

        // Mock recent visit
        $visit = Mockery::mock(ActualVisit::class);
        $visit->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $visit->shouldReceive('getAttribute')->with('visit_date')->andReturn(Carbon::now()->subDays(1));
        $visit->shouldReceive('getAttribute')->with('ll')->andReturn('31.2050');
        $visit->shouldReceive('getAttribute')->with('lg')->andReturn('29.9200');
        $visit->shouldReceive('getAttribute')->with('visit_address')->andReturn('Visit Address');

        // Mock all visit coordinates for frequency calculation
        $allVisit1 = Mockery::mock();
        $allVisit1->shouldReceive('getAttribute')->with('ll')->andReturn('31.2050');
        $allVisit1->shouldReceive('getAttribute')->with('lg')->andReturn('29.9200');

        $allVisit2 = Mockery::mock();
        $allVisit2->shouldReceive('getAttribute')->with('ll')->andReturn('31.2051');
        $allVisit2->shouldReceive('getAttribute')->with('lg')->andReturn('29.9201');

        $allVisit3 = Mockery::mock();
        $allVisit3->shouldReceive('getAttribute')->with('ll')->andReturn('31.3000');
        $allVisit3->shouldReceive('getAttribute')->with('lg')->andReturn('30.0000');

        // Mock Eloquent queries
        Account::shouldReceive('query')->andReturnSelf();
        Account::shouldReceive('select')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('get')->andReturn(collect([$account]));

        // Mock recent visits query
        ActualVisit::shouldReceive('where')->andReturnSelf();
        ActualVisit::shouldReceive('whereNotNull')->andReturnSelf();
        ActualVisit::shouldReceive('whereNull')->andReturnSelf();
        ActualVisit::shouldReceive('select')->andReturnSelf();
        ActualVisit::shouldReceive('orderBy')->andReturnSelf();
        ActualVisit::shouldReceive('limit')->andReturnSelf();
        ActualVisit::shouldReceive('get')->andReturn(collect([$visit]), collect([$allVisit1, $allVisit2, $allVisit3]));

        // Mock distance calculations for frequency (return values in order)
        $this->mockDistanceCalculator
            ->shouldReceive('distanceBetweenTwoCoordinates')
            ->andReturn(0.0, 50.0, 500.0); // Within radius: 2 visits (0m, 50m), outside: 1 visit (500m)

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('debug')->andReturnNull();

        $options = [
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $result = $this->service->generateLocationComparisonReport($options);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, count($result['accounts']));

        // Check that visit frequency is calculated correctly (2 visits within 100m radius)
        $visitLocation = $result['accounts'][0]['visit_locations'][0];
        $this->assertArrayHasKey('visit_frequency', $visitLocation);
        $this->assertEquals(2, $visitLocation['visit_frequency']); // Current visit + 1 within radius
    }

    /**
     * Test user information handling with missing user data
     */
    public function test_user_information_with_missing_user(): void
    {
        // Mock Account
        $account = Mockery::mock(Account::class);
        $account->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $account->shouldReceive('getAttribute')->with('name')->andReturn('Test Account');
        $account->shouldReceive('getAttribute')->with('code')->andReturn('ACC001');
        $account->shouldReceive('getAttribute')->with('address')->andReturn('Test Address');

        // Mock AccountLines (no registered location)
        AccountLines::shouldReceive('where')->andReturnSelf();
        AccountLines::shouldReceive('whereNotNull')->andReturnSelf();
        AccountLines::shouldReceive('orderBy')->andReturnSelf();
        AccountLines::shouldReceive('first')->andReturn(null);

        // Mock visit with missing user information
        $visit = Mockery::mock(ActualVisit::class);
        $visit->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $visit->shouldReceive('getAttribute')->with('visit_date')->andReturn(Carbon::now()->subDays(1));
        $visit->shouldReceive('getAttribute')->with('ll')->andReturn('31.2050');
        $visit->shouldReceive('getAttribute')->with('lg')->andReturn('29.9200');
        $visit->shouldReceive('getAttribute')->with('visit_address')->andReturn('Visit Address');
        $visit->shouldReceive('getAttribute')->with('user_id')->andReturn(null);
        $visit->shouldReceive('getAttribute')->with('user_name')->andReturn(null);
        $visit->shouldReceive('getAttribute')->with('user_email')->andReturn(null);

        // Mock Eloquent queries
        Account::shouldReceive('query')->andReturnSelf();
        Account::shouldReceive('select')->andReturnSelf();
        Account::shouldReceive('whereNull')->andReturnSelf();
        Account::shouldReceive('get')->andReturn(collect([$account]));

        ActualVisit::shouldReceive('where')->andReturnSelf();
        ActualVisit::shouldReceive('whereNotNull')->andReturnSelf();
        ActualVisit::shouldReceive('whereNull')->andReturnSelf();
        ActualVisit::shouldReceive('leftJoin')->andReturnSelf();
        ActualVisit::shouldReceive('select')->andReturnSelf();
        ActualVisit::shouldReceive('orderBy')->andReturnSelf();
        ActualVisit::shouldReceive('limit')->andReturnSelf();
        ActualVisit::shouldReceive('get')->andReturn(collect([$visit]), collect([$visit]));

        // Mock distance calculation for frequency
        $this->mockDistanceCalculator
            ->shouldReceive('distanceBetweenTwoCoordinates')
            ->andReturn(0.0);

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('debug')->andReturnNull();

        $result = $this->service->generateLocationComparisonReport();

        $this->assertTrue($result['success']);

        // Verify fallback user information is provided
        $visitLocation = $result['accounts'][0]['visit_locations'][0];
        $this->assertArrayHasKey('user', $visitLocation);
        $this->assertNull($visitLocation['user']['id']);
        $this->assertEquals('Unknown User', $visitLocation['user']['name']);
        $this->assertNull($visitLocation['user']['email']);
    }

    /**
     * Test parameter validation with LineDivision array parameters
     */
    public function test_validate_report_parameters_with_line_division_arrays(): void
    {
        // Mock Line model using Mockery alias
        $lineMock = Mockery::mock('alias:App\Line');
        $lineMock->shouldReceive('where')->with('id', 1)->andReturnSelf();
        $lineMock->shouldReceive('where')->with('id', 2)->andReturnSelf();
        $lineMock->shouldReceive('whereNull')->with('deleted_at')->andReturnSelf();
        $lineMock->shouldReceive('exists')->andReturn(true);

        // Mock LineDivision model using Mockery alias
        $divisionMock = Mockery::mock('alias:App\LineDivision');
        $divisionMock->shouldReceive('where')->with('id', 10)->andReturnSelf();
        $divisionMock->shouldReceive('where')->with('id', 20)->andReturnSelf();
        $divisionMock->shouldReceive('whereNull')->with('deleted_at')->andReturnSelf();
        $divisionMock->shouldReceive('where')->with('from_date', '<=', Mockery::any())->andReturnSelf();
        $divisionMock->shouldReceive('where')->with(Mockery::type('Closure'))->andReturnSelf();
        $divisionMock->shouldReceive('whereIn')->with('line_id', [1, 2])->andReturnSelf();
        $divisionMock->shouldReceive('exists')->andReturn(true);

        $options = [
            'line_ids' => [1, 2],
            'div_ids' => [10, 20],
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $result = $this->service->validateReportParameters($options);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test parameter validation with invalid line_ids
     */
    public function test_validate_report_parameters_invalid_line_ids(): void
    {
        // Mock Line model to return false for exists for invalid IDs
        $lineMock = Mockery::mock('alias:App\Line');
        $lineMock->shouldReceive('where')->with('id', 1)->andReturnSelf();
        $lineMock->shouldReceive('where')->with('id', 999)->andReturnSelf();
        $lineMock->shouldReceive('whereNull')->with('deleted_at')->andReturnSelf();
        $lineMock->shouldReceive('exists')->andReturn(true, false); // First ID exists, second doesn't

        $options = [
            'line_ids' => [1, 999]
        ];

        $result = $this->service->validateReportParameters($options);

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('line_ids', $result['errors']);
        $this->assertStringContainsString('999', $result['errors']['line_ids']);
    }

    /**
     * Test parameter validation with invalid div_ids
     */
    public function test_validate_report_parameters_invalid_div_ids(): void
    {
        // Mock LineDivision model to return false for exists for invalid IDs
        $divisionMock = Mockery::mock('alias:App\LineDivision');
        $divisionMock->shouldReceive('where')->with('id', 10)->andReturnSelf();
        $divisionMock->shouldReceive('where')->with('id', 999)->andReturnSelf();
        $divisionMock->shouldReceive('whereNull')->with('deleted_at')->andReturnSelf();
        $divisionMock->shouldReceive('where')->with('from_date', '<=', Mockery::any())->andReturnSelf();
        $divisionMock->shouldReceive('where')->with(Mockery::type('Closure'))->andReturnSelf();
        $divisionMock->shouldReceive('exists')->andReturn(true, false); // First ID exists, second doesn't

        $options = [
            'div_ids' => [10, 999]
        ];

        $result = $this->service->validateReportParameters($options);

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('div_ids', $result['errors']);
        $this->assertStringContainsString('999', $result['errors']['div_ids']);
    }
}
